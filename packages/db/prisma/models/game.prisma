model ProjectGame {
  /// @comment ID
  id                   String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId               String   @map("user_id")
  /// @comment 关联的项目ID
  projectId            String   @map("project_id")
  /// @comment 关联的公共游戏库ID
  publicGameLibraryId  String?  @map("public_game_library_id")
  /// @comment 名称
  name                 String   @map("name")
  /// @comment 宣传语
  slogan               String?  @map("slogan")
  /// @comment slug(游戏访问路径)
  slug                 String   @map("slug")
  /// @comment 分类id集合
  categories           String[] @map("categories")
  /// @comment 用户输入文本
  userInputText        String?  @map("user_input_text")
  /// @comment 是否为主游戏(主游戏用于首页展示)
  isPrimary            Boolean  @default(false) @map("is_primary")
  /// @comment 配置(多个配置项，根据key区分，key为国际化语言对应的key) Record<string, any>
  settings             Json?
  /// @comment 游戏类型 iframe|download|popup|placeholder @see api-types.ts GameType
  gameType             String   @default("iframe") @map("game_type")
  /// @comment 游戏信息基础配置 @see api-types.ts GameDownloadSettings
  gameDownloadSettings Json?    @map("game_download_settings")
  /// @comment 游戏区域背景配置 @see api-types.ts GameBackgroundSettings
  backgroundSettings   Json?    @map("background_settings")
  /// @comment 游戏iframe URL
  gameIframeUrl        String?  @map("game_iframe_url")
  /// @comment 截图URL
  screenshotUrl        String?  @map("screenshot_url")
  /// @comment 状态（PENDING、COMPLETED、FAILED） 用于自动添加时状态控制 @see consts/games.ts ProjectGameStatus
  status               String   @default("COMPLETED") @map("status")
  /// @comment 错误信息
  error                String?  @map("error")

  /// @comment 相关游戏配置(游戏ID集合)
  relatedGames       String[] @map("related_games")
  /// @comment 相关游戏配置(用于配置相关游戏展示规则,如：展示数量、展示规则等)
  relatedGamesConfig Json?    @map("related_games_config")

  /// @comment 相关视频配置 @see api-types.ts GameVideo
  relatedVideos Json? @map("related_videos")

  /// @comment 相关文章配置(文章ID集合)
  relatedArticles String[] @map("related_articles")

  /// @comment 评论配置（用于保存是否显示评论，以及与评论系统的关联配置信息如：链接地址）@see api-types.ts CommentsConfig
  commentsConfig Json?    @map("comments_config")
  /// @comment 所属标签（需要关联tag Id）
  tags           String[] @map("tags")
  /// @comment 创建时间
  createdAt      DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt      DateTime @updatedAt @map("updated_at")

  // 关联关系
  project     Project             @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user        User                @relation(fields: [userId], references: [id], onDelete: Cascade)
  gameLocales ProjectGameLocale[]

  @@unique([projectId, slug], name: "project_game_slug_unique")
  @@index([projectId])
  @@index([status])
  @@index([createdAt])
  @@index([userId])
  @@index([tags])
  @@index([categories])
  @@map("p_project_game")
}

// 游戏国际化
model ProjectGameLocale {
  /// @comment 设置ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 项目ID
  projectId String   @map("project_id")
  /// @comment 游戏ID
  gameId    String   @map("game_id")
  /// @comment 类型 metadata、content、tab_content @see consts/games.ts ProjectGameLocaleType
  type      String
  /// @comment 内容ID
  contentId String?  @map("content_id")
  /// @comment 语言
  locale    String
  /// @comment 内容 - 根据type不同，结构不同：
  /// - metadata: 存储 MetadataInfo 类型数据
  /// - content: 存储游戏内容数据
  /// - tab_content: 存储 TabContent[] 类型数据
  content   Json?
  /// @comment 文本
  text      String?  @db.Text
  /// @comment 排序
  sort      Int      @default(0)
  /// @comment 状态 PENDING、COMPLETED、FAILED @see consts/games.ts ProjectGameStatus
  status    String   @default("PENDING") @map("status")
  /// @comment 错误信息
  error     String?  @map("error")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  project Project     @relation(fields: [projectId], references: [id], onDelete: Cascade)
  /// @comment 游戏
  game    ProjectGame @relation(fields: [gameId], references: [id], onDelete: Cascade)

  @@unique([contentId, locale], name: "project_game_locale_content_id_locale_unique")
  @@index([projectId])
  @@index([gameId])
  @@index([locale])
  @@index([type])
  @@index([status])
  @@map("p_project_game_locale")
}

// 游戏任务记录模型
model GameTaskRecord {
  /// @comment 任务ID
  id                 String   @id @default(cuid()) @map("id")
  /// @comment 用户ID
  userId             String   @map("user_id")
  /// @comment 任务名称
  name               String   @map("name")
  /// @comment 任务状态
  status             String   @default("PENDING") @map("status")
  /// @comment 任务消息
  message            String?  @map("message")
  /// @comment 错误信息
  error              Json?    @map("error")
  /// @comment 用户输入文本
  user_input_text    String?  @map("user_input_text")
  /// @comment 是否为子游戏
  is_sub_game        Boolean  @default(false) @map("is_sub_game")
  /// @comment 是否覆盖页面路径
  override_page_path Boolean  @default(false) @map("override_page_path")
  /// @comment 当前步骤
  currentStep        String?  @map("current_step")
  /// @comment 自动部署
  auto_deploy        Boolean  @default(false) @map("auto_deploy")
  /// @comment 游戏类型
  gameType           String?  @map("game_type")
  /// @comment 游戏下载链接
  gameDownload       String?  @map("game_download")
  /// @comment 背景类型
  bgType             String?  @map("bg_type")
  /// @comment 背景图片
  bgImage            String?  @map("bg_image")
  /// @comment 背景视频
  bgVideo            String?  @map("bg_video")
  /// @comment 游戏iframe URL
  gameIframeUrl      String?  @map("game_iframe_url")
  /// @comment 截图URL
  screenshotUrl      String?  @map("screenshot_url")
  /// @comment 创建时间
  createdAt          DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt          DateTime @updatedAt @map("updated_at")
  /// @comment 关联的项目ID
  projectId          String   @map("project_id")
  /// @comment 任务类型
  taskType           String   @default("GAME_TASK") @map("task_type")
  /// @comment 任务数据
  taskData           String?  @map("task_data") @db.Text

  // 关联关系
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([projectId])
  @@index([status])
  @@index([createdAt])
  @@index([userId])
  @@map("t_game_task_record")
}

// 公共游戏库模型
model PublicGameLibrary {
  /// @comment ID
  id              String  @id @default(cuid()) @map("id")
  /// @comment 游戏名称
  gameName        String  @map("game_name")
  /// @comment 游戏描述
  gameDescription String? @map("game_description") @db.Text
  /// @comment 游戏类型
  gameType        String  @default("iframe") @map("game_type")

  /// @comment 游戏iframe URL
  gameIframeUrl String? @map("game_iframe_url") @db.VarChar(500)
  /// @comment 游戏截图URL
  screenshotUrl String? @map("screenshot_url") @db.VarChar(500)
  /// @comment 游戏图标URL
  iconUrl       String? @map("icon_url") @db.VarChar(500)

  /// @comment 游戏来源
  gameSource       String  @map("game_source")
  /// @comment 游戏原始分类
  originalCategory String? @map("original_category")
  /// @comment 本地分类
  localCategory    String? @map("local_category")
  /// @comment 游戏状态
  status           String  @default("PENDING") @map("status")
  /// @comment 元数据（JSON格式）
  metadata         Json?   @map("metadata")

  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("t_public_game_library")
}

// 公共游戏库爬虫任务记录模型
model PublicGameCrawlTask {
  /// @comment 任务ID
  id             String    @id @default(cuid()) @map("id")
  /// @comment 爬虫源
  source         String    @map("source")
  /// @comment 爬虫类型（全量或最新）
  crawlType      String    @map("crawl_type")
  /// @comment 爬虫状态
  status         String    @default("PENDING") @map("status")
  /// @comment 爬虫消息
  message        String?   @map("message")
  /// @comment 爬虫网址
  url            String?   @map("url")
  /// @comment 爬虫开始时间
  startTime      DateTime? @map("start_time")
  /// @comment 爬虫结束时间
  endTime        DateTime? @map("end_time")
  /// @comment 总游戏数
  totalGames     Int       @default(0) @map("total_games")
  /// @comment 成功数
  successCount   Int       @default(0) @map("success_count")
  /// @comment 重复数
  duplicateCount Int       @default(0) @map("duplicate_count")
  /// @comment 失败数
  failedCount    Int       @default(0) @map("failed_count")
  /// @comment 错误信息
  error          String?   @map("error") @db.Text
  /// @comment 创建时间
  createdAt      DateTime  @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt      DateTime  @updatedAt @map("updated_at")

  @@index([status])
  @@index([createdAt])
  @@index([source])
  @@map("t_public_game_crawl_task")
}

// 主游戏数据模型
model MasterGame {
  /// @comment 游戏ID
  id          String   @id @default(cuid()) @map("id")
  /// @comment 游戏名称
  name        String   @unique @map("name")
  /// @comment 来源数量
  sourceCount Int      @default(1) @map("source_count")
  /// @comment 来源列表 ["poki.com", "itch.io"]
  sources     String[] @map("sources")
  /// @comment 标签列表 ["trending_up", "featured"]
  tags        String[] @default([]) @map("tags")
  /// @comment 创建时间
  createdAt   DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt   DateTime @updatedAt @map("updated_at")

  // 关联
  gameItems      GameItem[]
  trendData      GameTrendData[]
  socialTasks    SocialMediaTask[]
  socialContents SocialMediaContent[]
  dailyStats     SocialMediaDailyStats[]

  @@index([name])
  @@map("t_master_game")
}

// 游戏数据模型
model GameItem {
  /// @comment 游戏ID
  id              String   @id @default(cuid()) @map("id")
  /// @comment 原始标题
  rawTitle        String   @map("raw_title")
  /// @comment AI清洗后的游戏名称
  cleanedName     String?  @map("cleaned_name")
  /// @comment 数据来源
  source          String   @map("source")
  /// @comment 原始URL
  sourceUrl       String   @unique @map("source_url")
  /// @comment 状态
  status          String   @default("RAW") @map("status")
  /// @comment 清洗重试次数
  cleanRetryCount Int      @default(0) @map("clean_retry_count")
  /// @comment 关联的游戏主数据ID
  masterGameId    String?  @map("master_game_id")
  /// @comment 创建时间
  createdAt       DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联
  masterGame MasterGame? @relation(fields: [masterGameId], references: [id], onDelete: SetNull)

  @@index([status])
  @@index([source])
  @@index([cleanedName])
  @@map("t_game_item")
}

// 游戏标签
model GameTag {
  /// @comment 标签ID
  id        String   @id @default(cuid()) @map("id")
  /// @comment 标签名称(中文用于显示)
  name      String   @map("name")
  /// @comment 标签slug
  slug      String   @map("slug")
  /// @comment 创建时间
  createdAt DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt DateTime @updatedAt @map("updated_at")

  // 关联
  locales GameTagLocale[]

  @@unique([slug], name: "game_tag_slug_unique")
  @@index([name])
  @@index([slug])
  @@map("t_game_tag")
}

// 游戏标签国际化内容
model GameTagLocale {
  /// @comment 标签ID
  id              String   @id @default(cuid()) @map("id")
  /// @comment 标签ID
  tagId           String   @map("tag_id")
  /// @comment 语言
  locale          String   @map("locale")
  /// @comment 标签名称
  name            String   @map("name")
  /// @comment 标签描述
  description     String?  @map("description")
  /// @comment 标签图片
  imageUrl        String?  @map("image_url")
  /// @comment 标签图标
  iconName        String?  @map("icon_name")
  /// @comment 标签标题
  metaTitle       String?  @map("meta_title")
  /// @comment 标签描述
  metaDescription String?  @map("meta_description")
  /// @comment 创建时间
  createdAt       DateTime @default(now()) @map("created_at")
  /// @comment 更新时间
  updatedAt       DateTime @updatedAt @map("updated_at")

  // 关联
  tag GameTag @relation(fields: [tagId], references: [id], onDelete: Cascade)

  @@unique([tagId, locale], name: "game_tag_locale_unique")
  @@index([tagId])
  @@index([locale])
  @@index([name])
  @@map("t_game_tag_locale")
}
